{"configurations": [{"name": "WinSDK", "includePath": ["${workspaceFolder}/**", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/um", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/shared", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/winrt"], "defines": [], "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.3x.x/bin/Hostx64/x64/cl.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}], "version": 4}